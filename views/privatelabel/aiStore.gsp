<%@ page import="javax.servlet.http.Cookie" %>
<%String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));

session.setAttribute("servername", appURL);
def newCookie = new javax.servlet.http.Cookie( "siteName", "privatelabel");
newCookie.path = "/"
response.addCookie newCookie;
%>
<g:render template="/privatelabel/navheader_new"></g:render>
<g:render template="/whitelabel/aiStoreDisplay"></g:render>

<g:render template="/privatelabel/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>

<g:render template="/whitelabel/aiStoreLogic"></g:render>

<style>
/* Fix background color issues */
body {
    background: #ffffff !important;
}

.page-main-wrapper {
    background: #ffffff !important;
}

/* Modern AI Store Styles */
.ai-store-container {
    background: #ffffff !important;
    min-height: 100vh;
    padding: 2rem 0;
}

.ai-store-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 3rem 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    border: none;
    position: relative;
    overflow: hidden;
}

.ai-store-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.ai-store-title {
    font-size: 3rem;
    font-weight: 800;
    color: #ffffff;
    text-align: center;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    letter-spacing: -0.02em;
    position: relative;
    z-index: 1;
}

.ai-store-subtitle {
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.3rem;
    font-weight: 500;
    margin-bottom: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    position: relative;
    z-index: 1;
}

/* Compact Search and Filters Row */
.ai-search-filters-row {
    background: #ffffff;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    display: flex;
    gap: 1rem;
    align-items: flex-end;
    flex-wrap: wrap;
}

.ai-search-container {
    position: relative;
    flex: 0 0 280px;
    transition: flex 0.3s ease;
    align-self: flex-end;
}

.ai-search-container.expanded {
    flex: 0 0 400px;
}

.ai-search-input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 0.9rem;
    background: #ffffff;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.ai-search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.ai-search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #667eea;
    font-size: 1.1rem;
    cursor: pointer;
}

.ai-filters-horizontal {
    display: flex;
    gap: 1rem;
    flex: 1;
    align-items: flex-end;
    flex-wrap: wrap;
}

.ai-filter-item {
    display: flex;
    flex-direction: column;
    min-width: 140px;
}

.ai-filter-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.ai-filter-select {
    padding: 0.6rem 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.85rem;
    background: #ffffff;
    transition: all 0.2s ease;
    cursor: pointer;
    min-width: 140px;
}

.ai-filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.searching-book-store {
    display: none !important;
}

.ai-filter-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-filter-icon {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.ai-books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 2rem;
    row-gap: 2.5rem;
    padding: 1.5rem 0;
}

.ai-book-card {
    background: #ffffff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.ai-book-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.ai-book-image-container {
    position: relative;
    width: 100%;
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.ai-book-image-wrapper {
    position: relative;
    width: 80%;
    max-width: 140px;
    height: auto;
    aspect-ratio: 3/4;
    border-radius: 0 6px 6px 0;
    overflow: hidden;
    box-shadow:
        inset 8px 0 10px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.15);
}

.ai-book-image-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    left: 5px;
    bottom: 0;
    width: 2px;
    background: rgba(0, 0, 0, 0.15);
    box-shadow: 1px 0 3px rgba(255, 255, 255, 0.4);
    z-index: 2;
}

.ai-book-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
    border-radius: 0 6px 6px 0;
    position: relative;
    z-index: 1;
}

.ai-book-card:hover .ai-book-image {
    transform: scale(1.05);
}

.ai-book-badge {
    position: absolute;
    bottom: -8px;
    right: -8px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border-radius: 50%;
    font-size: 0.65rem;
    font-weight: 700;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.5);
    z-index: 3;
    border: 3px solid white;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    line-height: 1;
}

.ai-book-content {
    padding: 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.ai-book-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.ai-book-publisher {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.ai-book-isbn {
    color: #adb5bd;
    font-size: 0.8rem;
    margin-bottom: 1rem;
}

.ai-book-price-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0;
    margin-top: auto;
}

.ai-book-price-section {
    display: flex;
    align-items: center;
    flex: 1;
}

.ai-book-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: #28a745;
}

.ai-book-original-price {
    color: #dc3545;
    text-decoration: line-through;
    font-size: 0.85rem;
    margin-right: 0.5rem;
}

.ai-book-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    margin-left: auto;
}

.ai-btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.ai-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.ai-btn-secondary {
    background: rgba(108, 117, 125, 0.1);
    border: 1px solid rgba(108, 117, 125, 0.2);
    color: #6c757d;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.ai-btn-secondary:hover {
    background: rgba(108, 117, 125, 0.2);
    color: #495057;
    text-decoration: none;
}

.ai-load-more {
    text-align: center;
    margin: 3rem 0;
}

.ai-load-more-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.ai-load-more-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.ai-no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: #ffffff;
    border-radius: 20px;
    margin: 2rem 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.ai-no-results-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.ai-no-results-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.ai-no-results-text {
    color: #6c757d;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .ai-search-filters-row {
        flex-direction: column;
        gap: 1.5rem;
    }

    .ai-search-container {
        flex: 1;
        max-width: 100%;
    }

    .ai-filters-horizontal {
        justify-content: center;
    }

    .ai-books-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 1.5rem;
        row-gap: 2rem;
    }
}

@media (max-width: 768px) {
    .ai-books-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        row-gap: 1.5rem;
        padding: 1rem;
    }

    .ai-store-header {
        padding: 2rem 1.5rem;
    }

    .ai-store-title {
        font-size: 2.2rem;
        font-weight: 800;
    }

    .ai-store-subtitle {
        font-size: 1.1rem;
    }

    .ai-book-content {
        padding: 0.75rem;
    }

    .ai-book-image-container {
        min-height: 160px;
        padding: 10px;
    }

    .ai-book-image-wrapper {
        max-width: 100px;
    }

    .ai-book-badge {
        width: 40px;
        height: 40px;
        font-size: 0.6rem;
        bottom: -6px;
        right: -6px;
    }

    .ai-search-filters-row {
        padding: 1rem;
        align-items: flex-start;
        flex-direction: column;
        gap: 1rem;
    }

    .ai-search-container {
        align-self: flex-start;
        flex: 1;
        max-width: 100%;
    }

    .ai-filters-horizontal {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        width: 100%;
    }

    .ai-clear-filters {
        align-self: flex-start;
    }

    .ai-filter-item {
        min-width: auto;
    }

    .ai-filter-select {
        min-width: auto;
    }
}

@media (max-width: 576px) {
    .ai-books-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        row-gap: 1.25rem;
        padding: 0.75rem;
    }

    .ai-store-container {
        padding: 1rem 0;
    }

    .ai-store-header,
    .ai-filters-container {
        margin: 0 1rem 1rem 1rem;
        padding: 1rem;
    }

    .ai-book-content {
        padding: 0.5rem;
    }

    .ai-book-title {
        font-size: 0.85rem;
        line-height: 1.2;
    }

    .ai-book-publisher {
        font-size: 0.75rem;
    }

    .ai-book-image-container {
        min-height: 140px;
        padding: 8px;
    }

    .ai-book-image-wrapper {
        max-width: 80px;
    }

    .ai-book-badge {
        width: 35px;
        height: 35px;
        font-size: 0.55rem;
        bottom: -5px;
        right: -5px;
        border: 2px solid white;
    }

    .ai-btn-primary {
        padding: 0.3rem 0.6rem;
        font-size: 0.7rem;
    }
}

@media (max-width: 400px) {
    .ai-books-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
        row-gap: 1rem;
        padding: 0.5rem;
    }

    .ai-book-image-container {
        min-height: 120px;
        padding: 6px;
    }

    .ai-book-image-wrapper {
        max-width: 70px;
    }

    .ai-book-content {
        padding: 0.4rem;
    }

    .ai-book-title {
        font-size: 0.8rem;
    }

    .ai-book-publisher {
        font-size: 0.7rem;
    }

    .ai-book-price {
        font-size: 1rem;
    }
}

/* Loading Animation */
.ai-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.ai-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.1);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: ai-spin 1s linear infinite;
}

@keyframes ai-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Form Controls */
.ai-form-control {
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.ai-form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.ai-clear-filters {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
    border: none !important;
    color: white !important;
    padding: 0.6rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    height: fit-content;
    align-self: flex-end;
}

.ai-clear-filters:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

/* Additional background fixes */
.page-main-wrapper,
.page-main-wrapper *,
section,
.container,
.row,
.col-12,
.col-lg-3,
.col-lg-9 {
    background: transparent !important;
}

html,
body {
    background-color: #ffffff !important;
}

/* Ensure filters display properly */
#filters {
    display: flex !important;
    visibility: visible !important;
}

#filters select {
    background: #ffffff !important;
    border: 2px solid #e9ecef !important;
    color: #495057 !important;
}

#filters select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}
</style>

<script>
// Enhanced hover effects for book cards
document.addEventListener("DOMContentLoaded", function() {
    var bookCards = document.querySelectorAll(".ai-book-card");

    for(var i = 0; i < bookCards.length; i++) {
        bookCards[i].addEventListener("mouseenter", function() {
            this.style.transform = "translateY(-8px) scale(1.02)";
        });

        bookCards[i].addEventListener("mouseleave", function() {
            this.style.transform = "translateY(0) scale(1)";
        });
    }
});
</script>
